import Colors from "@/config/colors"
import { textStyles } from "@/config/styles"
import useLoadingStore from "@/stores/loadingStore"
import React from "react"
import { useTranslation } from "react-i18next"
import { View, ActivityIndicator, StyleSheet, Text, Modal } from "react-native"
import { CustomPressable } from "./CustomPressable"

const Loading = () => {
  const { t } = useTranslation()
  const { setLoading, showCancelBtn } = useLoadingStore()
  return (
    <Modal visible={true} transparent={true} style={styles.container}>
      <View style={styles.container}>
        <ActivityIndicator size="large" color={Colors.Primary500} />
        {showCancelBtn && (
          <CustomPressable
            onPress={() => {
              setLoading(false)
            }}
          >
            <Text style={styles.cancelText}>{t("Cancel")}</Text>
          </CustomPressable>
        )}
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    backgroundColor: Colors.PalleteBlack80,
  },
  cancelText: {
    ...textStyles.LRegular,
    color: Colors.Primary500,
    marginTop: 8,
    textDecorationLine: "underline",
  },
})

export default Loading
