{"_format": "hh-sol-artifact-1", "contractName": "IEstateToken", "sourceName": "contracts/land/interfaces/IEstateToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "AuthorizedAccount", "type": "error"}, {"inputs": [], "name": "Deprecated", "type": "error"}, {"inputs": [], "name": "InsufficientFunds", "type": "error"}, {"inputs": [], "name": "InvalidCurrency", "type": "error"}, {"inputs": [], "name": "InvalidEstateId", "type": "error"}, {"inputs": [], "name": "InvalidInput", "type": "error"}, {"inputs": [], "name": "InvalidRate", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "InvalidTokenizer", "type": "error"}, {"inputs": [], "name": "InvalidUpdating", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "NotAuthorizedAccount", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "newValue", "type": "string"}], "name": "BaseURIUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "CommissionTokenUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "estateId", "type": "uint256"}], "name": "EstateDeprecation", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "estateId", "type": "uint256"}, {"indexed": false, "internalType": "uint40", "name": "expireAt", "type": "uint40"}], "name": "EstateExpirationExtension", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"indexed": true, "internalType": "uint256", "name": "tokenizationId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "tokenizer", "type": "address"}, {"indexed": false, "internalType": "uint40", "name": "tokenizeAt", "type": "uint40"}, {"indexed": false, "internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"indexed": false, "internalType": "uint8", "name": "decimals", "type": "uint8"}], "name": "NewToken", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newValue", "type": "uint256"}], "name": "RoyaltyRateUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "TokenizerAuthorization", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}], "name": "TokenizerDeauthorization", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"indexed": false, "internalType": "uint256[]", "name": "values", "type": "uint256[]"}], "name": "TransferBatch", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "TransferSingle", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "string", "name": "value", "type": "string"}, {"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}], "name": "URI", "type": "event"}, {"inputs": [], "name": "admin", "outputs": [{"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "at", "type": "uint256"}], "name": "balanceOfAt", "outputs": [{"internalType": "uint256", "name": "balance", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}], "name": "balanceOfBatch", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "commissionToken", "outputs": [{"internalType": "address", "name": "commissionToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "estateNumber", "outputs": [{"internalType": "uint256", "name": "tokenNumber", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "exists", "outputs": [{"internalType": "bool", "name": "existed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeReceiver", "outputs": [{"internalType": "address", "name": "feeReceiver", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}], "name": "getEstate", "outputs": [{"components": [{"internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"internalType": "uint256", "name": "tokenizationId", "type": "uint256"}, {"internalType": "address", "name": "tokenizer", "type": "address"}, {"internalType": "uint40", "name": "tokenizeAt", "type": "uint40"}, {"internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "bool", "name": "isDeprecated", "type": "bool"}], "internalType": "struct IEstateToken.Estate", "name": "tokenInfo", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRoyaltyRate", "outputs": [{"components": [{"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}], "internalType": "struct ICommon.Rate", "name": "rate", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}], "name": "isAvailable", "outputs": [{"internalType": "bool", "name": "isAvailable", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "values", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onERC1155BatchReceived", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onERC1155Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "salePrice", "type": "uint256"}], "name": "royaltyInfo", "outputs": [{"internalType": "address", "name": "receiver", "type": "address"}, {"internalType": "uint256", "name": "royaltyAmount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256[]", "name": "ids", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeBatchTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "totalSupply", "type": "uint256"}, {"internalType": "bytes32", "name": "zone", "type": "bytes32"}, {"internalType": "uint256", "name": "tokenizationId", "type": "uint256"}, {"internalType": "string", "name": "uri", "type": "string"}, {"internalType": "uint40", "name": "expireAt", "type": "uint40"}, {"internalType": "uint8", "name": "decimals", "type": "uint8"}, {"internalType": "address", "name": "commissionReceiver", "type": "address"}], "name": "tokenizeEstate", "outputs": [{"internalType": "uint256", "name": "estateId", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "totalSupply", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "uri", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "version", "outputs": [{"internalType": "string", "name": "version", "type": "string"}], "stateMutability": "pure", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}