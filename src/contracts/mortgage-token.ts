import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "@/config/env"
import mortgageTokenJson from "./abis/MortgageToken.json"
import { useReadContract, useWriteContract } from "wagmi"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { WalletAddress } from "@/service/types"

export const mortgageTokenAbi = mortgageTokenJson.abi

export const useIsMortgagesApprovedForAll = (userAddress: WalletAddress): boolean => {
  const { data } = useReadContract({
    abi: mortgageTokenAbi,
    address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
    functionName: "isApprovedForAll",
    args: [userAddress, CONTRACT_ADDRESS_MORTGAGE_TOKEN],
    query: {
      refetchInterval: 15000,
    },
  })
  return Boolean(data)
}

export const useRequestApproveForAllIfNeeded = (address: WalletAddress) => {
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const isApprovedForAll = useIsMortgagesApprovedForAll(address)

  const requestApproveForAll = async (): Promise<boolean> => {
    if (isApprovedForAll) {
      return true
    }
    if (!ethersProvider) throw new Error("Provider not available")
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
        abi: mortgageTokenAbi,
        functionName: "setApprovalForAll",
        args: [CONTRACT_ADDRESS_MORTGAGE_TOKEN, true],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status !== 1) {
        throw new Error("Transaction failed " + txHash)
      }
      return true
    } catch (error) {
      console.error("Error approving for all:", error)
      return false
    }
  }

  return { requestApproveForAllIfNeeded: requestApproveForAll }
}
