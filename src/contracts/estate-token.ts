import estateTokenJson from "./abis/EstateToken.json"
import { CONTRACT_ADDRESS_ESTATE_TOKEN } from "@/config/env"
import { useReadContract, useWriteContract } from "wagmi"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { WalletAddress } from "@/service/types"
export const estateTokenAbi = estateTokenJson.abi

export const useIsEstateApprovedForAll = (userAddress: WalletAddress): boolean => {
  const { data } = useReadContract({
    abi: estateTokenAbi,
    address: CONTRACT_ADDRESS_ESTATE_TOKEN,
    functionName: "isApprovedForAll",
    args: [userAddress, CONTRACT_ADDRESS_ESTATE_TOKEN],
    query: {
      refetchInterval: 15000,
    },
  })
  return Boolean(data)
}

export const useRequestApproveForAllIfNeeded = (address: WalletAddress) => {
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()

  const isApprovedForAll = useIsEstateApprovedForAll(address)

  const requestApproveForAll = async (): Promise<boolean> => {
    if (isApprovedForAll) {
      return true
    }
    if (!ethersProvider) throw new Error("Provider not available")
    try {
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_ESTATE_TOKEN,
        abi: estateTokenAbi,
        functionName: "setApprovalForAll",
        args: [CONTRACT_ADDRESS_ESTATE_TOKEN, true],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status !== 1) {
        throw new Error("Transaction failed " + txHash)
      }
      return true
    } catch (error) {
      console.error("Error approving for all:", error)
      return false
    }
  }

  return { requestApproveForAllIfNeeded: requestApproveForAll }
}
