import React from "react"
import { SimplePagingList } from "@/components/simplepaginglist"
import { MortgageLoan } from "@/service/types"
import { getListLoansApi } from "@/service"
import QueryKeys from "@/config/queryKeys"
import LoanItem from "./LoanItem"
import useAuthStore from "@/stores/authStore"
import { LoanActionButton } from "@/screens/shared/components/LoanActionButton"
import { useTranslation } from "react-i18next"
import { useLoanData } from "@/service/hooks/useLoanData"

const renderItem = (item: MortgageLoan) => {
  return (
    <LoanItem item={item} estate={item.estate} actionButton={<LoanActionButton loan={item} />} />
  )
}

const DisbursedLoanTab = () => {
  const { t } = useTranslation()
  const { address } = useAuthStore()
  const { getListLoan } = useLoanData()

  return (
    <SimplePagingList<MortgageLoan>
      getData={(params) => getListLoan(() => getListLoansApi({ ...params, owner: address }))}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      scrollEnabled={false}
      queryKeys={[QueryKeys.PROFILE.DISBURSED_LOAN]}
      emptyMessage={t("Wait for lenders to submit offers or share your deal to attract them.")}
      emptyTitle={t("No loan offers have been submitted")}
    />
  )
}

export default DisbursedLoanTab
