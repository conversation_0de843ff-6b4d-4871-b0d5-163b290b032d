import React from "react"
import { SimplePagingList } from "@/components/simplepaginglist"
import { Offer } from "@/service/types"
import { getListLoanSaleApi } from "@/service"
import QueryKeys from "@/config/queryKeys"
import LoanItem from "./LoanItem"
import OfferActionButton from "@/screens/shared/components/OfferActionButton"
import { useTranslation } from "react-i18next"
import { useLoanData } from "@/service/hooks/useLoanData"

const renderLoanSaleItem = (item: Offer) => {
  return (
    <LoanItem
      item={item.mortgageLoan}
      estate={item.estate}
      actionButton={<OfferActionButton offer={item} />}
    />
  )
}

const LoanSaleTab = () => {
  const { t } = useTranslation()
  const { getListOffer } = useLoanData()

  const getListLoanSale = (params: any) => {
    return getListOffer(() => getListLoanSaleApi(params))
  }
  return (
    <SimplePagingList<Offer>
      getData={getListLoanSale}
      renderItem={renderLoanSaleItem}
      keyExtractor={(item) => item.id}
      scrollEnabled={false}
      queryKeys={[QueryKeys.PROFILE.LOAN_SALE]}
      emptyMessage={t("Wait for lenders to submit offers or share your deal to attract them.")}
      emptyTitle={t("No disbursed loans have been submitted")}
    />
  )
}

export default LoanSaleTab
