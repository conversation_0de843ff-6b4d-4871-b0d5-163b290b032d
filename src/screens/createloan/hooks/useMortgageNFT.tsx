import { CONTRACT_ADDRESS_MORTGAGE_TOKEN } from "@/config/env"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { useTranslation } from "react-i18next"
import { useWriteContract } from "wagmi"
import { PublicEstate } from "@/service/types"
import { mortgageTokenAbi } from "@/contracts/mortgage-token"
import { durationUnitMap } from "@/utils/timeExt"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import { ContractFunctionExecutionError } from "viem"
import Logger from "@/utils/logger"
import { useCreateLoanContext } from "../context/CreateLoanContext"
import useAuthStore from "@/stores/authStore"
import { useRequestApproveForAllIfNeeded } from "@/contracts/estate-token"

const logger = new Logger({ tag: "useMortgageNFT" })

export const useMortgageNFT = () => {
  const { t } = useTranslation()
  const { address } = useAuthStore()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const { reset } = useCreateLoanContext()

  const { requestApproveForAllIfNeeded } = useRequestApproveForAllIfNeeded(address)

  // Execute the borrow transaction
  const executeBorrow = async (
    estate: PublicEstate,
    mortgageAmount: number,
    principal: string,
    repayment: string,
    duration: number,
    durationUnit: string,
  ) => {
    if (!ethersProvider) throw new Error(t("Provider not available"))
    const isApprovedForAll = await requestApproveForAllIfNeeded()
    if (!isApprovedForAll) {
      throw new Error(t("You need to approve the contract first"))
    }
    const { decimals, currency } = estate
    // Ensure values are numbers
    const mortgageAmountNum = Number(mortgageAmount)
    const principalNum = Number(principal)
    const repaymentNum = Number(repayment)
    const principalBigInt = BigInt(Math.round(principalNum * Math.pow(10, decimals)))
    const repaymentBigInt = BigInt(Math.round(repaymentNum * Math.pow(10, decimals)))

    const pow10 = BigInt(Math.pow(10, decimals))
    const txHash = await writeContractAsync({
      address: CONTRACT_ADDRESS_MORTGAGE_TOKEN,
      abi: mortgageTokenAbi,
      functionName: "borrow",
      args: [
        BigInt(estate.id),
        BigInt(mortgageAmountNum) * pow10,
        principalBigInt,
        repaymentBigInt,
        currency,
        duration * durationUnitMap[durationUnit],
      ],
    })
    const receipt = await ethersProvider.waitForTransaction(txHash)
    if (receipt.status === 1) {
      showSuccessWhenCallContract(
        `${t("Borrow success")}. ${t("Data will be updated in few seconds")}`,
      )
      reset()
    } else {
      throw new Error(t("Borrow failed" + txHash))
    }
  }

  // Main submit handler
  const onSubmit = async (
    estate: PublicEstate,
    mortgageAmount: number,
    principal: string,
    repayment: string,
    duration: number,
    durationUnit: string,
  ) => {
    const { currency } = estate
    if (
      !ethersProvider ||
      !Number(estate.id) ||
      !currency ||
      !mortgageAmount ||
      !principal ||
      !repayment
    ) {
      return
    }

    try {
      // await approveIfNeeded()
      await executeBorrow(estate, mortgageAmount, principal, repayment, duration, durationUnit)
    } catch (e: any) {
      if (e instanceof ContractFunctionExecutionError) {
        logger.error("Contract execution error details", {
          cause: e.cause,
          metaMessages: e.cause.metaMessages,
          docsPath: e.cause.docsPath,
        })
      }
      logger.error("Mortgage NFT error", e)
      showError(e.message)
    }
  }

  return { onSubmit }
}
