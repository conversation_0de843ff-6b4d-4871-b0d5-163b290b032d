import React from "react"
import { Image, StyleSheet, Text, View } from "react-native"
import { textStyles, viewStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { CardView, ExpandView, CustomPressable } from "src/components"
import { useTranslation } from "react-i18next"
import { calculateGridItemDimensions } from "src/utils/layout"
import InfoItem from "./InfoItem"
import icClock9 from "assets/images/ic_clock_9.png"
import icMapPin from "assets/images/ic_map_pin.png"
import icBoxLock from "assets/images/ic_box_lock.png"
import icBadgePercent from "assets/images/ic_badge_percent.png"
import { useCurrencies } from "@/screens/shared/hooks/useCurrencies"
import { convertDateFromTimeStamp, DateTimeFormat } from "@/utils/timeExt"
import { formatCurrencyByDecimals } from "@/utils/format"
import { PublicUser } from "@/service/types"
import LenderView from "./LenderView"
import { CurrencyView } from "./CurrencyView"
import { formatDecimalMax2Digits } from "@/utils/numberExt"
interface GridContentLoanViewProps {
  dueInSeconds: number
  mortgageAmount: string
  currency: string
  principal: string
  repayment: string
  apr: number
  decimals: number
  imageUrl: string
  name: string
  zone: string
  lender?: PublicUser
  buttonView: React.ReactNode
  onPress?: () => void
}

const { itemWidth } = calculateGridItemDimensions({
  numColumns: 2,
})

const GridContentLoanView: React.FC<GridContentLoanViewProps> = ({
  dueInSeconds,
  mortgageAmount,
  currency,
  principal,
  repayment,
  apr,
  decimals,
  imageUrl,
  name,
  zone,
  lender,
  buttonView,
  onPress,
}) => {
  const { t } = useTranslation()

  const { tokenSymbol, tokenImageUrl } = useCurrencies(currency)

  const displayDueDate = `${t("Due date")} ${convertDateFromTimeStamp(dueInSeconds * 1000, DateTimeFormat.SHORT)}`
  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)
  const displayCollateral = `${t("Collateral")} ${formattedMortgageAmount} NFTs`
  const displayApr = `${t("APR")} ${formatDecimalMax2Digits(apr)}%`

  const formattedPrincipal = formatCurrencyByDecimals(principal, decimals)
  const formattedRepayment = formatCurrencyByDecimals(repayment, decimals)
  const displayPrincipal = `${t("Principal")} ${formattedPrincipal}`
  const displayRepayment = `${t("Repayment")} ${formattedRepayment}`

  return (
    <CardView style={styles.card}>
      <CustomPressable onPress={onPress} enabled={!!onPress}>
        <View style={styles.container}>
          <View style={styles.topContainer}>
            <Image source={{ uri: imageUrl }} style={styles.image} />

            {dueInSeconds > 0 && (
              <InfoItem
                icon={icClock9}
                style={styles.dueDate}
                text={displayDueDate}
                iconStyle={styles.infoIconSmall}
                textStyle={styles.infoTextSmall}
              />
            )}

            {lender?.address && <LenderView lender={lender} style={styles.lender} />}
          </View>

          <Text style={styles.title} numberOfLines={2}>
            {name}
          </Text>

          <View style={styles.midContainer}>
            <InfoItem icon={icMapPin} text={zone} />
            <Text style={styles.dot}>•</Text>
            <InfoItem icon={icBoxLock} text={displayCollateral} />
            <Text style={styles.dot}>•</Text>
            <InfoItem icon={icBadgePercent} text={displayApr} />
          </View>

          <View style={styles.bottomContainer}>
            <CurrencyView
              textValue={displayPrincipal}
              tokenSymbol={tokenSymbol}
              tokenImageUrl={tokenImageUrl}
              style={styles.principal}
            />
            <CurrencyView
              textValue={displayRepayment}
              tokenSymbol={tokenSymbol}
              tokenImageUrl={tokenImageUrl}
              style={styles.repayment}
              textStyle={styles.repaymentText}
            />
          </View>
        </View>
      </CustomPressable>

      <ExpandView />

      {buttonView}
    </CardView>
  )
}

const styles = StyleSheet.create({
  card: {
    marginBottom: 12,
    flex: 1,
    width: itemWidth,
  },
  container: {
    width: "100%",
    flex: 1,
    padding: 6,
    gap: 6,
  },
  topContainer: {
    width: "100%",
  },
  image: {
    borderRadius: 6,
    height: 100,
    resizeMode: "cover",
  },
  dueDate: {
    position: "absolute",
    bottom: 0,
    left: 0,
    margin: 4,
    padding: 4,
    backgroundColor: Colors.PalleteBlack,
    borderRadius: 2,
  },
  infoIconSmall: {
    ...viewStyles.size8Icon,
    tintColor: Colors.Neutral300,
  },
  infoTextSmall: {
    ...textStyles.XSMedium,
    color: Colors.Neutral300,
  },
  lender: {
    position: "absolute",
    top: 0,
    right: 0,
    margin: 4,
  },
  title: {
    ...textStyles.MMedium,
    minHeight: 28,
  },
  midContainer: {
    width: "100%",
    flexDirection: "row",
    flexWrap: "wrap",
  },
  dot: {
    color: Colors.PalleteWhite,
    marginHorizontal: 4,
  },
  bottomContainer: {
    gap: 4,
    width: "100%",
  },
  principal: {
    backgroundColor: Colors.Neutral900,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 2,
    alignSelf: "flex-start",
  },
  repayment: {
    backgroundColor: Colors.Primary900,
    paddingHorizontal: 6,
    paddingVertical: 4,
    borderRadius: 2,
    alignSelf: "flex-start",
  },
  repaymentText: {
    color: Colors.Primary300,
  },
})

export default GridContentLoanView
