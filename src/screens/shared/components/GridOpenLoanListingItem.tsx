import React from "react"
import { StyleSheet, Text, View, ViewStyle } from "react-native"
import { textStyles } from "src/config/styles"
import Colors from "src/config/colors"
import { GridContentLoanView } from "src/screens/shared/components"
import { MortgageLoan } from "@/service/types"
import { useTranslation } from "react-i18next"
import { LoanActionButton } from "./LoanActionButton"
import * as Routes from "src/navigation/Routers"
import { useAppNavigaton } from "src/navigation/Routers"

interface GridOpenLoanListingItemProps {
  item: MortgageLoan
  style?: ViewStyle
}

const GridOpenLoanListingItem: React.FC<GridOpenLoanListingItemProps> = ({ item, style }) => {
  const { t } = useTranslation()
  const {
    id,
    dueInSeconds,
    mortgageAmount,
    currency,
    principal,
    repayment,
    apr,
    estate: {
      decimals,
      metadata: {
        imageUrl,
        metadata: {
          name,
          locale_detail: { zone },
        },
      },
    },
  } = item

  const navigation = useAppNavigaton()
  const handleNavigateDetail = () => {
    navigation.navigate(Routes.LOAN_DETAIL, { id })
  }

  const renderLendNowButton = () => (
    <View style={styles.positiveButton}>
      <Text style={styles.positiveButtonText}>{t("Lend Now")}</Text>
    </View>
  )

  const renderForeCloseButton = () => (
    <View style={styles.negativeButton}>
      <Text style={styles.negativeButtonText}>{t("Foreclose")}</Text>
    </View>
  )

  const renderCancelButton = () => (
    <View style={styles.negativeButton}>
      <Text style={styles.negativeButtonText}>{t("Cancel")}</Text>
    </View>
  )

  const renderRepayButton = () => (
    <View style={styles.positiveButton}>
      <Text style={styles.positiveButtonText}>{t("Repay")}</Text>
    </View>
  )

  return (
    <View style={style}>
      <GridContentLoanView
        onPress={handleNavigateDetail}
        buttonView={
          <LoanActionButton
            loan={item}
            lendButtonContent={renderLendNowButton()}
            foreCloseButtonContent={renderForeCloseButton()}
            cancelButtonContent={renderCancelButton()}
            repayButtonContent={renderRepayButton()}
          />
        }
        dueInSeconds={dueInSeconds}
        mortgageAmount={mortgageAmount}
        currency={currency}
        principal={principal}
        repayment={repayment}
        apr={apr}
        decimals={decimals}
        imageUrl={imageUrl}
        name={name}
        zone={zone}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  positiveButton: {
    borderRadius: 4,
    margin: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    alignItems: "center",
    backgroundColor: Colors.Success300,
  },
  positiveButtonText: {
    ...textStyles.MMedium,
    color: Colors.PalleteBlack,
  },
  negativeButton: {
    borderRadius: 4,
    margin: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    alignItems: "center",
    backgroundColor: Colors.Neutral700,
  },
  negativeButtonText: {
    ...textStyles.MMedium,
    color: Colors.Neutral900,
  },
})

export default GridOpenLoanListingItem
