import React from "react"
import {
  View,
  Text,
  Image,
  StyleSheet,
  StyleProp,
  ViewStyle,
  TextStyle,
  ImageStyle,
} from "react-native"
import Colors from "@/config/colors"
import { textStyles, viewStyles } from "@/config/styles"

export enum CurrencyViewType {
  SYMBOL = "symbol",
  IMAGE = "image",
}

interface CurrencyViewProps {
  textValue: string
  tokenSymbol: string
  tokenImageUrl: string
  currencyViewType?: CurrencyViewType
  style?: StyleProp<ViewStyle>
  textStyle?: StyleProp<TextStyle>
  imageStyle?: StyleProp<ImageStyle>
}

export const CurrencyView: React.FC<CurrencyViewProps> = ({
  textValue,
  tokenSymbol,
  tokenImageUrl,
  currencyViewType = CurrencyViewType.IMAGE,
  style,
  textStyle,
  imageStyle,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={[styles.text, textStyle]}>{textValue}</Text>
      {currencyViewType === CurrencyViewType.SYMBOL ? (
        <Text style={[styles.text, textStyle]}>{` ${tokenSymbol}`}</Text>
      ) : (
        <Image source={{ uri: tokenImageUrl }} style={[styles.image, imageStyle]} />
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    alignSelf: "flex-start",
  },
  text: {
    ...textStyles.SMedium,
    color: Colors.Neutral100,
  },
  image: {
    ...viewStyles.size10Icon,
    marginLeft: 4,
  },
})
