import { z } from "zod"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { useTranslation } from "react-i18next"
import { CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE } from "src/config/env"
import { showError, showSuccessWhenCallContract } from "@/utils/toast"
import { mortgageMarketplaceAbi } from "@/contracts/mortgage-marketplace"
import { useEthersProvider } from "@/hooks/useEthersProvider"
import { useWriteContract, useChainId } from "wagmi"
import Logger from "src/utils/logger"
import { MortgageLoan } from "@/service/types"
import { formatCurrencyByDecimals } from "@/utils"
import { useCurrencies } from "./useCurrencies"
import useLoadingStore from "@/stores/loadingStore"
import { useRequestApproveForAllIfNeeded } from "@/contracts/mortgage-token"
import useAuthStore from "@/stores/authStore"

const logger = new Logger({ tag: "useSellLoan" })

const useFormSchema = () => {
  const { t } = useTranslation()

  return z.object({
    sellPrice: z.string().min(1, {
      message: t("Please input a valid sell price greater than 0"),
    }),
  })
}

export const useSellLoan = (loan: MortgageLoan, onRefresh?: () => void) => {
  const { t } = useTranslation()
  const { address } = useAuthStore()
  const { setLoading } = useLoadingStore()
  const ethersProvider = useEthersProvider()
  const { writeContractAsync } = useWriteContract()
  const { requestApproveForAllIfNeeded } = useRequestApproveForAllIfNeeded(address)

  const chainId = useChainId()

  const {
    estate: { decimals },
    principal,
    mortgageAmount,
    currency,
  } = loan

  const formatedPrincipal = formatCurrencyByDecimals(principal, decimals)
  const formattedMortgageAmount = formatCurrencyByDecimals(mortgageAmount, decimals)
  const { tokenSymbol, tokenImageUrl } = useCurrencies(currency)

  const formSchema = useFormSchema()
  type Payload = z.infer<typeof formSchema>
  const form = useForm<Payload>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sellPrice: "",
    },
  })

  const chainNamesMap: Record<number, string> = {
    56: t("Binance Smart Chain"),
    97: t("Binance Smart Chain Testnet"),
  }

  const chainName = chainNamesMap[chainId] || t("Unknown Chain")
  const sellPrice = form.watch("sellPrice")
  const isSellingPositive = Number(sellPrice) > 0

  const onSubmit = async (data: Payload) => {
    if (!ethersProvider || !CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE) return
    setLoading(true)
    try {
      const isApprovedForAll = await requestApproveForAllIfNeeded()
      if (!isApprovedForAll) {
        throw new Error(t("You need to approve the contract first"))
      }
      const priceBigInt = BigInt(Number(data.sellPrice) * Math.pow(10, decimals))
      const txHash = await writeContractAsync({
        address: CONTRACT_ADDRESS_MORTGAGE_MARKETPLACE,
        abi: mortgageMarketplaceAbi,
        functionName: "list",
        args: [BigInt(loan.id), priceBigInt, currency],
      })
      const receipt = await ethersProvider.waitForTransaction(txHash)
      if (receipt.status === 1) {
        showSuccessWhenCallContract(
          t("Listing success") + ". " + t("Data will be updated in few seconds"),
        )
        onRefresh?.()
      } else {
        throw new Error(t("Transaction failed") + " " + txHash)
      }
    } catch (e: any) {
      showError(t("Listing failed" + e))
      logger.error("List for sale failed", e)
    } finally {
      setLoading(false)
    }
  }

  const resetForm = () => {
    form.reset()
    setLoading(false)
  }

  return {
    form,
    formatedPrincipal,
    formattedMortgageAmount,
    tokenSymbol,
    tokenImageUrl,
    chainName,
    sellPrice,
    isSellingPositive,
    onSubmit,
    resetForm,
  }
}
