import QueryKeys from "@/config/queryKeys"
import { getListLoansApi } from "@/service"
import { MortgageLoanState } from "@/service/types"
import { useQuery } from "@tanstack/react-query"
import useLoadingStore from "@/stores/loadingStore"
import { useEffect, useCallback, useState } from "react"
import { useLoanData } from "@/service/hooks/useLoanData"

export const useAllLoans = (isFocused: boolean = true) => {
  const { setLoading } = useLoadingStore()
  const { getListLoan } = useLoanData()

  const result = useQuery({
    queryKey: [QueryKeys.EXPLORER.ALL_LOANS],
    queryFn: () =>
      getListLoan(() =>
        getListLoansApi({
          itemsPerPage: 100,
          states: MortgageLoanState.PENDING,
        }),
      ),
    refetchInterval: isFocused ? 10_000 : false,
    refetchIntervalInBackground: false,
    enabled: isFocused,
  })

  const { data, isLoading, error, refetch } = result

  const [isRefreshing, setIsRefreshing] = useState(false)
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    await refetch()
    setIsRefreshing(false)
  }, [refetch, setIsRefreshing])

  useEffect(() => {
    setLoading(isLoading || isRefreshing)
    return () => setLoading(false)
  }, [isLoading, isRefreshing, setLoading])

  const contextValue = {
    loans: data?.list || [],
    totalLoans: data?.pagination.totalItems || 0,
    isLoading,
    error,
    handleRefresh,
    isRefreshing,
  }

  return { contextValue }
}
