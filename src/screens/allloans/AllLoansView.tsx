import React, { useCallback } from "react"
import { StyleSheet, FlatList, View, Text, RefreshControl } from "react-native"
import { useAllLoansContext } from "./context/AllLoansContext"
import { EmptyView, Background } from "@/components"
import { useTranslation } from "react-i18next"
import { MortgageLoan } from "@/service/types"
import { GridOpenLoanListingItem } from "@/screens/shared/components"
import { textStyles } from "@/config/styles"

const AllLoansView: React.FC = () => {
  const {
    loans = [],
    totalLoans,
    isLoading,
    error,
    handleRefresh,
    isRefreshing,
  } = useAllLoansContext()
  const { t } = useTranslation()

  const renderItem = useCallback(
    ({ item }: { item: MortgageLoan }) => <GridOpenLoanListingItem item={item} />,
    [],
  )

  const title = t("{{count}} Open Loan Listings", { count: totalLoans })

  const renderContent = () => {
    if (isLoading) {
      return null
    }

    if (error) {
      return <EmptyView subtitle={t("Failed to load loans")} />
    }

    if (!loans.length) {
      return <EmptyView subtitle={t("No loans found")} />
    }

    return (
      <View style={styles.container}>
        <Text style={styles.title}>{title}</Text>
        <FlatList
          data={loans}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.flatListContent}
          columnWrapperStyle={styles.columnWrapper}
          refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={handleRefresh} />}
        />
      </View>
    )
  }

  return <Background>{renderContent()}</Background>
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  title: {
    ...textStyles.size2XLMedium,
    margin: 16,
  },
  flatListContent: {
    paddingHorizontal: 16,
  },
  columnWrapper: {
    justifyContent: "space-between",
  },
})

export default AllLoansView
