import React from "react"
import { useAllLoans } from "./hooks/useAllLoans"
import AllLoansView from "./AllLoansView"
import { AllLoansContext } from "./context/AllLoansContext"
import { useIsFocused } from "@react-navigation/native"

const AllLoansScreen: React.FC = () => {
  const isFocused = useIsFocused()
  const { contextValue } = useAllLoans(isFocused)

  return (
    <AllLoansContext.Provider value={contextValue}>
      <AllLoansView />
    </AllLoansContext.Provider>
  )
}

export default AllLoansScreen
