import { useQueries } from "@tanstack/react-query"
import { useTranslation } from "react-i18next"
import {
  getHomeHighInterestLoansApi,
  getListLoansApi,
  getListLoanSaleApi,
  getTotalVolumeApi,
} from "@/service"
import { useLoanData } from "@/service/hooks/useLoanData"
import { QueryKeys } from "@/config/queryKeys"
import useLoadingStore from "@/stores/loadingStore"
import { useCallback, useEffect, useMemo, useState } from "react"
import { MortgageLoanState, OfferState, TotalVolume } from "@/service/types"

export enum SectionType {
  FEATURED = "FEATURED",
  HIGH_INTEREST = "HIGH_INTEREST",
  OPEN_LOAN_LISTINGS = "OPEN_LOAN_LISTINGS",
  LOAN_SALES_MARKETPLACE = "LOAN_SALES_MARKETPLACE",
  REPAID_LOANS = "REPAID_LOANS",
}

export interface Section {
  type: SectionType
  title: string
  data: any
}

export interface FeaturedData {
  totalVolume: TotalVolume
  openLoans: number
  completedLoans: number
  description: string
}

export const useHome = (isFocused: boolean = true) => {
  const { t } = useTranslation()
  const { setLoading } = useLoadingStore()
  const { getListLoan, getListOffer } = useLoanData()

  const result = useQueries({
    queries: [
      {
        queryKey: [QueryKeys.HOME.HIGH_INTEREST_LOANS],
        queryFn: async () => {
          const loans = await getHomeHighInterestLoansApi()
          return getListLoan(() =>
            Promise.resolve({
              list: loans,
              pagination: { totalItems: loans.length, itemsPerPage: loans.length, currentPage: 1 },
            }),
          )
        },
        refetchInterval: isFocused ? 10_000 : false,
        refetchIntervalInBackground: false,
        enabled: isFocused,
      },
      {
        queryKey: [QueryKeys.HOME.OPEN_LOAN_LISTINGS],
        queryFn: () =>
          getListLoan(() =>
            getListLoansApi({ states: MortgageLoanState.PENDING, itemsPerPage: 6 }),
          ),
        refetchInterval: isFocused ? 10_000 : false,
        refetchIntervalInBackground: false,
        enabled: isFocused,
      },
      {
        queryKey: [QueryKeys.HOME.LOAN_SALES_MARKETPLACE],
        queryFn: () =>
          getListOffer(() => getListLoanSaleApi({ states: OfferState.SELLING, itemsPerPage: 6 })),
        refetchInterval: isFocused ? 10_000 : false,
        refetchIntervalInBackground: false,
        enabled: isFocused,
      },
      {
        queryKey: [QueryKeys.HOME.REPAID_LOANS],
        queryFn: () =>
          getListLoan(() => getListLoansApi({ states: MortgageLoanState.REPAID, itemsPerPage: 6 })),
        refetchInterval: isFocused ? 10_000 : false,
        refetchIntervalInBackground: false,
        enabled: isFocused,
      },
      {
        queryKey: [QueryKeys.HOME.TOTAL_VOLUME],
        queryFn: getTotalVolumeApi,
        refetchInterval: isFocused ? 10_000 : false,
        refetchIntervalInBackground: false,
        enabled: isFocused,
      },
    ],
  })

  const [highInterestLoans, openLoanListings, loanSalesMarketplace, repaidLoans, totalVolume] =
    result

  const isLoading = result.some((query) => query.isLoading)

  const [isRefreshing, setIsRefreshing] = useState(false)
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    await Promise.all(result.map((query) => query.refetch()))
    setIsRefreshing(false)
  }, [result, setIsRefreshing])

  useEffect(() => {
    setLoading(isLoading || isRefreshing)
    return () => setLoading(false)
  }, [isLoading, isRefreshing, setLoading])

  const isErrorAll =
    highInterestLoans.isError &&
    openLoanListings.isError &&
    loanSalesMarketplace.isError &&
    repaidLoans.isError &&
    totalVolume.isError

  const featuredData: FeaturedData = useMemo(() => {
    return {
      totalVolume: totalVolume.data?.[0] ?? {
        currency: "",
        totalPrincipal: "0",
      },
      openLoans: openLoanListings.data?.pagination.totalItems ?? 0,
      completedLoans: repaidLoans.data?.pagination.totalItems ?? 0,
      description: t(
        "Briky Lend is the leading lending platform for tokenized real estate! Borrow stablecoins by collateralizing legally verified real estate NFTs — fast, transparent, and without banks. Briky supports all land NFTs on the Briky Land platform, fully backed and recorded on-chain.",
      ),
    }
  }, [t, openLoanListings.data, repaidLoans.data, totalVolume.data])

  const sections = useMemo<Section[]>(
    () => [
      {
        type: SectionType.FEATURED,
        title: t("Leverage Every Property"),
        data: featuredData,
      },
      {
        type: SectionType.HIGH_INTEREST,
        title: t("High-Interest"),
        data: highInterestLoans.data?.list ?? [],
      },
      {
        type: SectionType.OPEN_LOAN_LISTINGS,
        title: t("Open Loan Listings"),
        data: openLoanListings.data?.list ?? [],
      },
      {
        type: SectionType.LOAN_SALES_MARKETPLACE,
        title: t("Loan Sales Marketplace"),
        data: loanSalesMarketplace.data?.list ?? [],
      },
      {
        type: SectionType.REPAID_LOANS,
        title: t("Repaid Loans"),
        data: repaidLoans.data?.list ?? [],
      },
    ],
    [t, featuredData, highInterestLoans, openLoanListings, loanSalesMarketplace, repaidLoans],
  )

  const isAllHasNoData = useMemo(() => {
    return sections.every((section) => section.data.length === 0)
  }, [sections])

  const contextValue = {
    sections,
    isLoading,
    isErrorAll,
    isAllHasNoData,
    handleRefresh,
    isRefreshing,
  }

  return {
    contextValue,
  }
}
