import React from "react"
import { HomeContext } from "./context/HomeContext"
import { useHome } from "./hooks/useHome"
import HomeView from "./HomeView"
import { useIsFocused } from "@react-navigation/native"

const HomeScreen: React.FC = () => {
  const isFocused = useIsFocused()
  const { contextValue } = useHome(isFocused)

  return (
    <HomeContext.Provider value={contextValue}>
      <HomeView />
    </HomeContext.Provider>
  )
}

export default HomeScreen
