import { useQueryWithErrorHandling } from "src/config/queryClient"
import QueryKeys from "src/config/queryKeys"
import { MortgageLoan } from "@/service/types"
import { useLoanData } from "@/service/hooks/useLoanData"

export const useLoanDetail = (id: string, isFocused: boolean = true) => {
  const { getLoanDetailById } = useLoanData()

  return useQueryWithErrorHandling<MortgageLoan>({
    queryKey: QueryKeys.LOAN.DETAIL(id || ""),
    queryFn: async () => {
      const loan = await getLoanDetailById(id || "")
      if (!loan) {
        throw new Error(`Không tìm thấy khoản vay với ID: ${id}`)
      }
      return loan
    },
    refetchInterval: isFocused ? 10_000 : false,
    refetchIntervalInBackground: false,
    enabled: isFocused,
  })
}
