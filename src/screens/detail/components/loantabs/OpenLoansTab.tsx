import React from "react"
import SimplePagingList from "@/components/simplepaginglist/SimplePagingList"
import QueryKeys from "src/config/queryKeys"
import LoanItemView from "src/screens/detail/components/LoanItemView"
import { MortgageLoan } from "@/service/types"
import { getListLoansApi } from "@/service"
import { LoanActionButton } from "@/screens/shared/components/LoanActionButton"
import { useLoanDetailContext } from "src/screens/detail/context/LoanDetailContext"
import { useLoanData } from "@/service/hooks/useLoanData"

const OpenLoansTab: React.FC = () => {
  const { openTabStates, loan } = useLoanDetailContext()
  const { getListLoan } = useLoanData()
  const estateId = loan?.estate.id

  const renderItem = (item: MortgageLoan) => {
    return <LoanItemView item={item} actionButton={<LoanActionButton loan={item} />} />
  }

  return (
    <SimplePagingList<MortgageLoan>
      getData={(params) =>
        getListLoan(() => getListLoansApi({ ...params, states: openTabStates, estateId }))
      }
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      scrollEnabled={false}
      queryKeys={QueryKeys.LOAN.LIST(openTabStates)}
    />
  )
}

export default OpenLoansTab
