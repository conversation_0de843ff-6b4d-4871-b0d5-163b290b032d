import React from "react"
import { View, Text, Image, StyleSheet, Linking } from "react-native"
import Colors from "src/config/colors"
import { CustomPressable } from "@/components"
import { textStyles, viewStyles } from "src/config/styles"
import { useTranslation } from "react-i18next"
import { shortenAddress } from "src/utils/stringExt"
import InfoItem from "./InfoItem"
import clockIcon from "assets/images/ic_clock_9.png"
import mapPinIcon from "assets/images/ic_map_pin.png"
import arrowUpRightIcon from "assets/images/ic_arrow_up_right.png"
import { getElapsedTime } from "src/utils/timeExt"
import { getFullAddress, LocaleDetail } from "@/service/types"
import { useNavigateProfile } from "src/screens/detail/hooks/useNavigateProfile"
import { BASE_WEB_URL } from "@/config/env"
import commonAvatarIcon from "assets/images/ic_common_avatar.png"

interface BasicSectionLoanViewProps {
  estateId: string
  image: string
  name: string
  borrowerAvatar: string
  borrowerAddress: string
  blockTimestamp: number
  address: string
  locale_detail: LocaleDetail
}

const BasicSectionLoanView: React.FC<BasicSectionLoanViewProps> = ({
  estateId,
  image,
  name,
  borrowerAvatar,
  blockTimestamp,
  address,
  borrowerAddress,
  locale_detail,
}) => {
  const { t } = useTranslation()
  const borrowerAddressShortened = shortenAddress(borrowerAddress)
  const onNavigateToProfile = useNavigateProfile()
  const elapsedTime = getElapsedTime(blockTimestamp, t)
  const createdAtFormatted = `${t("Posted on")} ${elapsedTime}`

  const fullAddress = getFullAddress(address, locale_detail)

  const handleViewMore = () => {
    const url = `${BASE_WEB_URL}/estate/${estateId}`
    Linking.openURL(url)
  }

  return (
    <View style={styles.container}>
      <View style={styles.imageContent}>
        <Image source={{ uri: image }} style={styles.image} fadeDuration={0} />
      </View>
      <Text style={styles.name} numberOfLines={2}>
        {name}
      </Text>
      <CustomPressable
        style={styles.creatorContainer}
        onPress={() => {
          onNavigateToProfile(borrowerAddress)
        }}
      >
        <Text style={styles.byText}>{t("By")}</Text>
        <Image
          source={
            borrowerAvatar
              ? {
                  uri: borrowerAvatar,
                }
              : commonAvatarIcon
          }
          style={styles.avatar}
        />
        <Text style={textStyles.XLBold}>{borrowerAddressShortened}</Text>
      </CustomPressable>
      <InfoItem
        style={styles.infoRow}
        icon={clockIcon}
        text={createdAtFormatted}
        iconStyle={styles.infoIcon}
        textStyle={styles.infoText}
      />
      <InfoItem
        style={[styles.infoRow, styles.flex1]}
        icon={mapPinIcon}
        text={fullAddress}
        iconStyle={styles.infoIcon}
        textStyle={[styles.infoText, styles.flex1]}
      />
      <CustomPressable style={styles.viewMore} onPress={handleViewMore}>
        <Text style={textStyles.LMedium}>{t("View More Details")}</Text>
        <Image source={arrowUpRightIcon} style={styles.more} />
      </CustomPressable>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 16,
  },
  name: {
    ...textStyles.size3XLMedium,
    marginBottom: 8,
    marginTop: 16,
  },
  creatorContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  byText: {
    ...textStyles.XLBold,
    marginRight: 6,
  },
  avatar: {
    ...viewStyles.size20Icon,
    resizeMode: "cover",
    marginRight: 6,
  },
  flex1: {
    flex: 1,
  },
  infoRow: {
    marginBottom: 8,
  },
  infoIcon: {
    ...viewStyles.size16Icon,
    marginRight: 8,
    tintColor: Colors.Neutral300,
  },
  infoText: {
    ...textStyles.LMedium,
    color: Colors.Neutral300,
  },
  viewMore: {
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderRadius: 6,
    borderColor: Colors.Neutral900,
    paddingHorizontal: 12,
    marginTop: 16,
    alignSelf: "flex-start",
    paddingVertical: 8,
  },
  imageContent: {
    height: 192,
    borderRadius: 6,
  },
  image: {
    flex: 1,
    width: "100%",
    borderRadius: 6,
  },
  more: {
    ...viewStyles.size16Icon,
    marginStart: 4,
  },
})

export default BasicSectionLoanView
