import React, { useCallback } from "react"
import {
  View,
  FlatList,
  StyleSheet,
  ListRenderItem,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  RefreshControl,
} from "react-native"
import { LoanDetailSectionItem } from "./types"
import SectionRenderer from "./components/SectionRenderer"
import { Background, EmptyView } from "@/components"
import { useTranslation } from "react-i18next"
import { useLoanDetailContext } from "./context/LoanDetailContext"
import LoanDetailHeader from "./components/LoanHeaderView"

const LoanDetailContent: React.FC = () => {
  const { t } = useTranslation()
  const { loanDetailError, sections, isLoadingLoanDetail } = useLoanDetailContext()

  const renderItem: ListRenderItem<LoanDetailSectionItem> = useCallback(({ item }) => {
    return <SectionRenderer item={item} />
  }, [])

  const keyExtractor = useCallback((item: LoanDetailSectionItem) => item.id, [])

  if (loanDetailError && !isLoadingLoanDetail) {
    return <EmptyView subtitle={t("Failed to load loan details")} />
  }

  if ((!sections || sections.length === 0) && !isLoadingLoanDetail) {
    return <EmptyView subtitle={t("No loan details found")} />
  }

  if (isLoadingLoanDetail) {
    return null
  }

  return (
    <FlatList
      data={sections}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      scrollEnabled={false}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainer}
    />
  )
}

const LoanDetailView = () => {
  const { loan, isRefreshing, pullToRefresh } = useLoanDetailContext()

  //TODO: need improve to not need wrap Scrollview out Flatlist
  return (
    <Background>
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollViewContent}
        refreshControl={<RefreshControl refreshing={isRefreshing} onRefresh={pullToRefresh} />}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : undefined}
          style={styles.keyboardAvoidingView}
        >
          <View style={styles.container}>
            <LoanDetailHeader state={loan?.state} />
            <LoanDetailContent />
          </View>
        </KeyboardAvoidingView>
      </ScrollView>
    </Background>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  scrollViewContent: {
    paddingBottom: 50,
  },
})

export default LoanDetailView
