import QueryKeys from "@/config/queryKeys"
import { getListLoanSaleApi } from "@/service"
import { OfferState } from "@/service/types"
import { useQuery } from "@tanstack/react-query"
import useLoadingStore from "@/stores/loadingStore"
import { useEffect, useCallback, useState } from "react"
import { useLoanData } from "@/service/hooks/useLoanData"

export const useLoanSale = (isFocused: boolean = true) => {
  const { setLoading } = useLoadingStore()
  const { getListOffer } = useLoanData()

  const result = useQuery({
    queryKey: [QueryKeys.EXPLORER.LOAN_SALE],
    queryFn: () =>
      getListOffer(() =>
        getListLoanSaleApi({
          itemsPerPage: 100,
          states: OfferState.SELLING,
        }),
      ),
    refetchInterval: isFocused ? 10_000 : false,
    refetchIntervalInBackground: false,
    enabled: isFocused,
  })

  const { data, isLoading, error, refetch } = result

  const [isRefreshing, setIsRefreshing] = useState(false)
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    await refetch()
    setIsRefreshing(false)
  }, [refetch, setIsRefreshing])

  useEffect(() => {
    setLoading(isLoading || isRefreshing)
    return () => setLoading(false)
  }, [isLoading, isRefreshing, setLoading])

  const contextValue = {
    offers: data?.list || [],
    totalOffers: data?.pagination.totalItems || 0,
    isLoading,
    error,
    handleRefresh,
    isRefreshing,
  }

  return { contextValue }
}
