import React from "react"
import { LoanSaleContext } from "./context/LoanSaleContext"
import { useLoanSale } from "./hooks/useLoanSale"
import LoanSaleView from "./LoanSaleView"
import { useIsFocused } from "@react-navigation/native"

const LoanSaleScreen: React.FC = () => {
  const isFocused = useIsFocused()
  const { contextValue } = useLoanSale(isFocused)

  return (
    <LoanSaleContext.Provider value={contextValue}>
      <LoanSaleView />
    </LoanSaleContext.Provider>
  )
}

export default LoanSaleScreen
