import { createContext, useContext } from "react"
import { Offer } from "@/service/types"

interface LoanSaleContextType {
  offers: Offer[]
  totalOffers: number
  isLoading: boolean
  error: Error | null
  handleRefresh: () => void
  isRefreshing: boolean
}

export const LoanSaleContext = createContext<LoanSaleContextType>({
  offers: [],
  totalOffers: 0,
  isLoading: false,
  error: null,
  handleRefresh: () => {},
  isRefreshing: false,
})

export const useLoanSaleContext = () => {
  return useContext(LoanSaleContext)
}
