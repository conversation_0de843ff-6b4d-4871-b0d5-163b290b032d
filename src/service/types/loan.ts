import { Estate, PublicUser } from "./index"

// Keep model for UI
export enum MortgageLoanState {
  PENDING = "PENDING",
  SUPPLIED = "SUPPLIED",
  OVERDUE = "OVERDUE",
  REPAID = "REPAID",
  FORECLOSED = "FORECLOSED",
  CANCELLED = "CANCELLED",
}

export type MortgageLoan = {
  id: string
  mortgageAmount: string
  currency: string
  principal: string
  repayment: string
  state: MortgageLoanState
  durationInSeconds: number
  dueInSeconds: number
  apr: number
  blockTimestamp: number
  estate: Estate
  owner: PublicUser
  borrower: PublicUser
  lender: PublicUser
}

export enum OfferState {
  SELLING = "SELLING",
  CANCELLED = "CANCELLED",
  OVERDUE = "OVERDUE",
  SOLD = "SOLD",
}

export type Offer = {
  id: string
  tokenId: string
  price: string
  currency: string
  sellerAddress: string
  state: OfferState
  due: number
  mortgageLoan: MortgageLoan
  estate: Estate
}

export type Transaction = {
  blockNumber: string
  from: string
  hash: string
  timestamp: string
  to: string
}

export type OfferSale = {
  buyer: PublicUser
  commissionAmount: string
  commissionReceiver: string
  estateTokenId: string
  id: string
  mortgageLoan: MortgageLoan
  offer: Offer
  offerId: string
  royaltyAmount: string
  royaltyReceiver: string
  seller: PublicUser
  tokenId: string
  transaction: Transaction
}

// Model for BE

export type MortgageLoanBE = Omit<MortgageLoan, "estate"> & {
  estateTokenId: string
}

export type OfferBE = Omit<Offer, "estate"> & {
  estateTokenId: string
}

export type OfferSaleBE = Omit<OfferSale, "mortgageLoan" | "offer"> & {
  mortgageLoan: MortgageLoanBE
  offer: OfferBE
}
