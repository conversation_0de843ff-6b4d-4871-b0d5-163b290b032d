import defaultEstateData from "assets/datas/estate.json"

export enum EstateZone {
  VIETNAM = "VIETNAM",
}

export enum CredentialType {
  VN001 = "VN001",
}

export enum ApplicationStatus {
  APPLICATION_VALIDATING = "APPLICATION_VALIDATING",
  APPLICATION_CANCELLED = "APPLICATION_CANCELLED",
  REQUEST_SELLING = "REQUEST_SELLING",
  REQUEST_CANCELLED = "REQUEST_CANCELLED",
  REQUEST_EXPIRED = "REQUEST_EXPIRED",
  REQUEST_CONFIRMED = "REQUEST_CONFIRMED",
  REQUEST_INSUFFICIENT_SOLD_AMOUNT = "REQUEST_INSUFFICIENT_SOLD_AMOUNT",
  REQUEST_TRANSFERRING_OWNERSHIP = "REQUEST_TRANSFERRING_OWNERSHIP",
}

export enum TokenizationRequestState {
  CONFIRMED = "CONFIRMED",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
  SELLING = "SELLING",
  INSUFFICIENT_SOLD_AMOUNT = "INSUFFICIENT_SOLD_AMOUNT",
  TRANSFERRING_OWNERSHIP = "TRANSFERRING_OWNERSHIP",
}

export type LocaleDetail = {
  zone: EstateZone
  vietnam: {
    level1: string
    level2: string
    level3: string
  }
}

export type FileSchema = {
  id: number
  created_at_in_seconds: number
  name: string
  content_type: string
  path: string
  bucket: string
}

export type EstateTokenAttribute = {
  trait_type: string
  value: string
}

export enum EstateTokenAreaUnit {
  SQM = "sqm",
  SQFT = "sqft",
}

export type EstateTokenArea = {
  area: number
  unit: EstateTokenAreaUnit
}

export enum ApplicationType {
  PUBLIC_SALE = "PUBLIC_SALE",
}

export type BrokerWallet = {
  address: string
  broker: Broker
  brokerID: number
  createdAt: string
  deletedAt: DeletedAt
  id: number
  updatedAt: string
}

export type Broker = {
  alias: string
  avatarFile: FileType
  avatarFileID: number
  createdAt: string
  deletedAt: DeletedAt
  dob: string
  email: string
  emailVerificationCode: string
  emailVerificationCodeExpiredAt: string
  emailVerified: boolean
  hashedPassword: string
  id: number
  isAdmin: boolean
  name: string
  updatedAt: string
  wallet: string[]
}

export type KycData = {
  vietnam: {
    date_of_birth_in_seconds: number
    full_name: string
    national_id: string
    national_id_card_back_image_file_id: number
    national_id_card_front_image_file_id: number
  }
  zone: EstateZone
}

export enum UserStatus {
  VERIFIED = "VERIFIED",
  UNVERIFIED = "UNVERIFIED",
}

export type Requester = {
  address: string
  alias: string
  avatarFile: FileType
  avatarFileID: number
  brokerWallet: BrokerWallet
  createdAt: string
  deletedAt: DeletedAt
  dob: string
  email: string
  isManager: boolean
  isModerator: boolean
  isVerified: boolean
  kycdata: KycData
  name: string
  nationality: string
  phone: string
  status: UserStatus
  updatedAt: string
  vietnamNationalIDCardBackImageFile: FileType
  vietnamNationalIDCardFrontImageFile: FileType
}

export type Application = {
  brokerAddress: string
  cancellationReason: string
  createdAt: string
  currency: string
  decimals: number
  deletedAt: DeletedAt
  duration: number
  expiredAt: number
  id: number
  isCancelled: boolean
  maxSellingAmount: string
  metadata: string
  metadataID: number
  minSellingAmount: string
  requester: Requester
  requesterAddress: string
  tokenizationRequestID: string
  totalSupply: string
  type: ApplicationType
  unitPrice: string
  updatedAt: string
}

export enum AppraisalPartyType {
  LAWYER = "LAWYER",
}

export type AppraisalParty = {
  createdAt: string
  deletedAt: string
  id: number
  name: string
  type: AppraisalPartyType
  updatedAt: string
}

export type DeletedAt = {
  time: string
  valid: boolean
}

export type FileType = {
  bucket: string
  contentType: string
  createdAt: string
  deletedAt: DeletedAt
  id: number
  name: string
  path: string
  updatedAt: string
}

export type Issuer = {
  address: string
  createdAt: string
  deletedAt: DeletedAt
  id: number
  locale_detail: LocaleDetail
  name: string
  type: AppraisalPartyType
  updatedAt: string
}

export enum RequirementType {
  // Chứng nhận quyền sỡ hữu/sử dụng bất động sản
  OWNERSHIP_CERTIFICATE = "OWNERSHIP_CERTIFICATE",
  // Xác minh danh tính công ty
  COMPANY_IDENTITY_VERIFICATION = "COMPANY_IDENTITY_VERIFICATION",
  // Xác minh danh tính chủ pháp nhân tại Việt Nam
  LEGAL_REPRESENTATIVE_VERIFICATION = "LEGAL_REPRESENTATIVE_VERIFICATION",
  // Xác minh mối liên hệ với công ty mẹ
  PARENT_COMPANY_RELATIONSHIP_VERIFICATION = "PARENT_COMPANY_RELATIONSHIP_VERIFICATION",
  // Xác minh danh tính chủ bất động sản
  OWNER_IDENTITY_VERIFICATION = "OWNER_IDENTITY_VERIFICATION",
  // Chuyển nhượng bất động sản từ chủ bất động sản sang cho công ty
  TRANSFER_FROM_OWNER = "TRANSFER_FROM_OWNER",
  // Niêm phong bất động sản
  PROPERTY_SEALING = "PROPERTY_SEALING",
  // Thẩm định pháp lý bất động sản
  LEGAL_ASSESSMENT = "LEGAL_ASSESSMENT",
  // Tình trạng hôn thú của chủ bất động sản
  MARITAL_STATUS = "MARITAL_STATUS",
  // Định giá bất động sản
  ESTATE_VALUATION = "ESTATE_VALUATION",
  // Thuế thu nhập cá nhân
  INCOME_TAX = "INCOME_TAX",
  // Thuế giá trị gia tăng
  VAT = "VAT",
  // Phí trước bạ
  REGISTRATION_FEE = "REGISTRATION_FEE",
  // Thuế tài nguyên
  RESOURCE_TAX = "RESOURCE_TAX",
  // Cập nhật chứng nhận quyền sở hữu/sử dụng bất động sản
  CERTIFICATE_UPDATE = "CERTIFICATE_UPDATE",
  // Xác nhận tình trạng thế chấp
  MORTGAGE_STATUS = "MORTGAGE_STATUS",
  // Chuyển nhượng bất động sản từ đơn vị nhận thế chấp sang cho công ty
  TRANSFER_FROM_MORTGAGEE = "TRANSFER_FROM_MORTGAGEE",
  // Giải chấp
  MORTGAGE_RELEASE = "MORTGAGE_RELEASE",
}

export type DocumentsType = {
  createdAt: string
  deletedAt: DeletedAt
  file: FileType
  file_id: number
  id: number
  issuer: Issuer
  issuer_id: number
  metadata: string
  metadata_id: number
  requirement_type: RequirementType
  updatedAt: string
  zone: EstateZone
}

export type LandRegistryOffice = {
  createdAt: string
  deletedAt: DeletedAt
  id: number
  name: string
  type: AppraisalPartyType
  updatedAt: string
}

export type NewTokenEvent = {
  createAt: number
  createdAt: string
  decimals: string
  deletedAt: DeletedAt
  expiredAt: number
  id: number
  requestID: string
  tokenID: string
  transactionHash: string[]
  updatedAt: string
  uri: string
}

export type EstateType = {
  brokerAddress: string
  createAt: number
  createdAt: string
  decimals: string
  deletedAt: DeletedAt
  expiredAt: number
  id: string
  isDeprecated: boolean
  metadata: string
  newTokenEvent: NewTokenEvent
  tokenizationRequest: TokenizationRequest
  tokenizationRequestID: string
  totalSupply: string
  updatedAt: string
  uri: string
}

export type TokenizationRequest = {
  brokerAddress: string
  createdAt: string
  currency: string
  decimals: number
  deletedAt: DeletedAt
  estateID: string
  expiredAt: number
  id: string
  maxSellingAmount: string
  metadata: string
  minSellingAmount: string
  publicSaleEndsAt: number
  requester: Requester
  requesterAddress: string
  soldAmount: string
  state: TokenizationRequestState
  totalSupply: string
  unitPrice: string
  updatedAt: string
  uri: string
}

export type ApplicationRequestMetadata = {
  address: string
  application: Application
  appraisal_document: FileSchema[]
  appraisal_party: AppraisalParty
  appraisal_party_id: number
  area: EstateTokenArea
  attributes: EstateTokenAttribute[]
  broker_address: string
  created_at: string
  credential_id: string
  credential_photos: FileSchema[]
  credential_type: CredentialType
  deleted_at: DeletedAt
  description: string
  documents: DocumentsType[]
  estate: EstateType
  estate_photos: FileSchema[]
  id: number
  image: string
  image_file: FileSchema
  land_registry_documents: FileSchema[]
  land_registry_office: LandRegistryOffice
  land_registry_office_id: number
  locale_detail: LocaleDetail
  name: string
  request: TokenizationRequest
  requester_address: string
  status: ApplicationStatus
  tokenized_at: string
  updated_at: string
  uri: string
}

export type RequestMetadata = {
  metadata: ApplicationRequestMetadata
  imageUrl: string
  credentialPhotoUrls: string[]
  estatePhotoUrls: string[]
  landRegistryDocumentUrls: string[]
  appraisalDocumentUrls: string[]
}

export type Estate = {
  id: string
  uri: string
  totalSupply: string
  decimals: number
  createAtInSeconds: number
  expireAtInSeconds: number
  isDeprecated: boolean
  broker: string
  tokenizationRequest: TokenizationRequest
  metadata: RequestMetadata
  balance: string
}

export type PublicEstate = {
  id: string
  totalSupply: string
  imageUrl: string
  name: string
  balance: string
  initialUnitPrice: string
  decimals: number
  currency: string
}

export const getFullAddress = (address: string, locale_detail: LocaleDetail) => {
  return `${address}, ${locale_detail.vietnam.level3}, ${locale_detail.vietnam.level2}, ${locale_detail.vietnam.level1} `
}

// Default Estate constant
export const DEFAULT_ESTATE: Estate = defaultEstateData as Estate
