import { getEstateByIdApi, getEstatesByIdsApi, getLoanDetailByIdApi } from ".."
import {
  ListResponse,
  MortgageLoanBE,
  MortgageLoan,
  Offer,
  OfferBE,
  OfferSale,
  OfferSaleBE,
  DEFAULT_ESTATE,
} from "../types"
export const useLoanData = () => {
  const getListLoan = async (
    apiFunc: () => Promise<ListResponse<MortgageLoanBE>>,
  ): Promise<ListResponse<MortgageLoan>> => {
    const { list: loans, pagination } = await apiFunc()
    const estateIds = loans.map((loan) => loan.estateTokenId)
    const { list: estates } = await getEstatesByIdsApi(estateIds)
    const loansWithEstates: MortgageLoan[] = loans.map((loan) => {
      const foundEstate =
        estates.find((estate) => estate.id === loan.estateTokenId) || DEFAULT_ESTATE
      return { ...loan, estate: foundEstate }
    })
    return {
      list: loansWithEstates,
      pagination,
    }
  }

  const getLoanDetailById = async (id: string): Promise<MortgageLoan | undefined> => {
    const loanDetail = await getLoanDetailByIdApi(id)
    const estate = (await getEstateByIdApi(loanDetail.estateTokenId)) || DEFAULT_ESTATE
    return { ...loanDetail, estate }
  }

  const getListOffer = async (
    apiFunc: () => Promise<ListResponse<OfferBE>>,
  ): Promise<ListResponse<Offer>> => {
    const { list: offers, pagination } = await apiFunc()
    const estateIds = offers.map((offer) => offer.estateTokenId)
    const { list: estates } = await getEstatesByIdsApi(estateIds)
    const offersWithEstates: Offer[] = offers.map((offer) => {
      const foundEstate =
        estates.find((estate) => estate.id === offer.estateTokenId) || DEFAULT_ESTATE
      return { ...offer, estate: foundEstate }
    })
    return {
      list: offersWithEstates,
      pagination,
    }
  }

  const getListOfferSale = async (
    apiFunc: () => Promise<ListResponse<OfferSaleBE>>,
  ): Promise<ListResponse<OfferSale>> => {
    const { list: offerSales, pagination } = await apiFunc()
    const estateIds = offerSales.map((offerSale) => offerSale.estateTokenId)
    const { list: estates } = await getEstatesByIdsApi(estateIds)

    const offerSalesWithEstates: OfferSale[] = offerSales.map((offerSale) => {
      const foundEstate =
        estates.find((estate) => estate.id === offerSale.estateTokenId) || DEFAULT_ESTATE

      // Transform the mortgageLoan and offer to include estate
      const mortgageLoan = {
        ...offerSale.mortgageLoan,
        estate: foundEstate,
      }

      const offer = {
        ...offerSale.offer,
        estate: foundEstate,
      }

      return {
        ...offerSale,
        mortgageLoan,
        offer,
      }
    })

    return {
      list: offerSalesWithEstates,
      pagination,
    }
  }

  return {
    getListLoan,
    getLoanDetailById,
    getListOffer,
    getListOfferSale,
  }
}
