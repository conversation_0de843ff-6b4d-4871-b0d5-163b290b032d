import React from "react"
import { View, StyleSheet, StatusBar } from "react-native"
import { NavigationIndependentTree } from "@react-navigation/native"
import AppNavigator from "@/navigation/AppNavigator"
import "@/i18n/config"
import WalletConnectProvider from "@/providers/WalletConnectProvider"
import useLoadingStore from "@/stores/loadingStore"
import Loading from "src/components/Loading"
import AuthProvider from "@/providers/auth/AuthProvider"
import usePrepareApp from "@/hooks/usePrepareApp"
import { SafeAreaProvider, useSafeAreaInsets } from "react-native-safe-area-context"
import { ActionSheetProvider } from "@expo/react-native-action-sheet"
import Colors from "@/config/colors"

export default function App() {
  const { isLoading } = useLoadingStore()
  const { appIsReady, onLayoutRootView } = usePrepareApp()

  if (!appIsReady) {
    return null
  }

  return (
    <View style={styles.container} onLayout={onLayoutRootView}>
      <StatusBar animated={true} backgroundColor={Colors.PalleteBlack} barStyle={"light-content"} />
      <WalletConnectProvider>
        <>
          <ActionSheetProvider>
            <AuthProvider>
              <NavigationIndependentTree>
                <SafeAreaProvider>
                  <SafeAreaContent>
                    <AppNavigator />
                  </SafeAreaContent>
                </SafeAreaProvider>
              </NavigationIndependentTree>
            </AuthProvider>
          </ActionSheetProvider>
          {isLoading && <Loading />}
        </>
      </WalletConnectProvider>
    </View>
  )
}

const SafeAreaContent: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const insets = useSafeAreaInsets()
  return (
    <View
      style={[styles.safeAreaContent, { paddingTop: insets.top, backgroundColor: Colors.Black }]}
    >
      {children}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  safeAreaContent: {
    flex: 1,
  },
})
